"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-setup/page",{

/***/ "(app-pages-browser)/./src/lib/services/setupService.ts":
/*!******************************************!*\
  !*** ./src/lib/services/setupService.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SetupService: function() { return /* binding */ SetupService; }\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass SetupService {\n    /**\r\n   * Check if this is the first user (admin) in the system\r\n   */ static async isFirstUser() {\n        try {\n            const usersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(usersQuery);\n            return snapshot.empty;\n        } catch (error) {\n            console.error(\"Error checking if first user:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * Create the admin user and default organization\r\n   */ static async createAdminUser(firebaseUser, displayName) {\n        try {\n            // Create admin user document - filter out undefined values\n            const adminUserData = {\n                id: firebaseUser.uid,\n                email: firebaseUser.email,\n                displayName: displayName || firebaseUser.displayName || \"Admin\",\n                role: \"admin\",\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                preferences: {\n                    theme: \"light\",\n                    notifications: true\n                }\n            };\n            // Only add photoURL if it exists\n            if (firebaseUser.photoURL) {\n                adminUserData.photoURL = firebaseUser.photoURL;\n            }\n            const adminUser = adminUserData;\n            // Create default organization\n            const defaultOrganization = {\n                id: \"default-org\",\n                name: \"\".concat(adminUser.displayName, \"'s Organization\"),\n                description: \"Default organization for the admin user\",\n                ownerId: firebaseUser.uid,\n                members: {\n                    [firebaseUser.uid]: {\n                        role: \"admin\",\n                        joinedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n                    }\n                },\n                settings: {\n                    aiModels: {\n                        \"anthropic/claude-3-haiku\": {\n                            enabled: true,\n                            displayName: \"Claude 3 Haiku (Fast)\",\n                            provider: \"openrouter\"\n                        },\n                        \"anthropic/claude-3-sonnet\": {\n                            enabled: true,\n                            displayName: \"Claude 3 Sonnet (Balanced)\",\n                            provider: \"openrouter\"\n                        },\n                        \"openai/gpt-4\": {\n                            enabled: true,\n                            displayName: \"GPT-4 (Advanced)\",\n                            provider: \"openrouter\"\n                        },\n                        \"openai/gpt-3.5-turbo\": {\n                            enabled: true,\n                            displayName: \"GPT-3.5 Turbo (Fast)\",\n                            provider: \"openrouter\"\n                        }\n                    },\n                    mcpEnabled: false // Disabled by default for security\n                },\n                apiKeys: {\n                    openrouter: [] // Will be configured in admin panel\n                },\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n            };\n            // Save both documents\n            await Promise.all([\n                (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\", firebaseUser.uid), adminUser),\n                (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"), defaultOrganization)\n            ]);\n            return {\n                user: adminUser,\n                organization: defaultOrganization\n            };\n        } catch (error) {\n            console.error(\"Error creating admin user:\", error);\n            // Provide more specific error messages\n            if (error.code === \"permission-denied\") {\n                throw new Error(\"Permission denied. Please deploy Firestore security rules first. See FIRESTORE_SETUP.md for instructions.\");\n            } else if (error.code === \"unavailable\") {\n                throw new Error(\"Firestore is currently unavailable. Please try again later.\");\n            } else if (error.code === \"failed-precondition\") {\n                throw new Error(\"Firestore operation failed. Please check your Firebase configuration.\");\n            }\n            throw new Error(\"Failed to create admin user: \".concat(error.message));\n        }\n    }\n    /**\r\n   * Check if the system has been initialized\r\n   */ static async isSystemInitialized() {\n        try {\n            const orgDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"));\n            return orgDoc.exists();\n        } catch (error) {\n            console.error(\"Error checking system initialization:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * Get the default organization\r\n   */ static async getDefaultOrganization() {\n        try {\n            const orgDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"organizations\", \"default-org\"));\n            if (orgDoc.exists()) {\n                return {\n                    id: orgDoc.id,\n                    ...orgDoc.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error getting default organization:\", error);\n            return null;\n        }\n    }\n    /**\r\n   * Create a regular user (non-admin)\r\n   */ static async createRegularUser(firebaseUser, displayName) {\n        try {\n            // Create user document - filter out undefined values\n            const userData = {\n                id: firebaseUser.uid,\n                email: firebaseUser.email,\n                displayName: displayName || firebaseUser.displayName || \"\",\n                role: \"user\",\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n                preferences: {\n                    theme: \"light\",\n                    notifications: true\n                }\n            };\n            // Only add photoURL if it exists\n            if (firebaseUser.photoURL) {\n                userData.photoURL = firebaseUser.photoURL;\n            }\n            const user = userData;\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\", firebaseUser.uid), userData);\n            return user;\n        } catch (error) {\n            console.error(\"Error creating regular user:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/setupService.ts\n"));

/***/ })

});