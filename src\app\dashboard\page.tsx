'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/layout/DashboardLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import { 
  PlusIcon,
  FolderIcon,
  RectangleStackIcon,
  ListBulletIcon,
  CalendarIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { useProjectStore, useTaskStore } from '@/lib/store'
import Link from 'next/link'

// Mock data for demonstration
const mockStats = {
  totalProjects: 5,
  totalTasks: 23,
  completedTasks: 15,
  upcomingDeadlines: 3
}

const mockRecentTasks = [
  {
    id: '1',
    title: 'Design new landing page',
    project: 'Website Redesign',
    priority: 'high' as const,
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
    status: 'in-progress' as const
  },
  {
    id: '2',
    title: 'Review user feedback',
    project: 'Product Research',
    priority: 'medium' as const,
    dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
    status: 'todo' as const
  },
  {
    id: '3',
    title: 'Update documentation',
    project: 'API Development',
    priority: 'low' as const,
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    status: 'review' as const
  }
]

const mockUpcomingEvents = [
  {
    id: '1',
    title: 'Team Standup',
    time: '9:00 AM',
    date: 'Today'
  },
  {
    id: '2',
    title: 'Client Review Meeting',
    time: '2:00 PM',
    date: 'Tomorrow'
  },
  {
    id: '3',
    title: 'Sprint Planning',
    time: '10:00 AM',
    date: 'Friday'
  }
]

export default function DashboardPage() {
  const { projects } = useProjectStore()
  const { tasks } = useTaskStore()

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
      case 'urgent':
        return 'text-red-600 bg-red-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'low':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'done':
        return 'text-green-600 bg-green-100'
      case 'in-progress':
        return 'text-blue-600 bg-blue-100'
      case 'review':
        return 'text-purple-600 bg-purple-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Good morning! 👋</h1>
            <p className="text-gray-600 mt-1">Here's what's happening with your projects today.</p>
          </div>
          <Link
            href="/projects/new"
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Project
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FolderIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Projects</p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.totalProjects}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <ListBulletIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Tasks</p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.totalTasks}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.completedTasks}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <ClockIcon className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Due Soon</p>
                <p className="text-2xl font-bold text-gray-900">{mockStats.upcomingDeadlines}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Tasks */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">Recent Tasks</h2>
                  <Link href="/tasks" className="text-sm text-primary-600 hover:text-primary-700">
                    View all
                  </Link>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {mockRecentTasks.map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{task.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{task.project}</p>
                        <div className="flex items-center space-x-2 mt-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(task.priority)}`}>
                            {task.priority}
                          </span>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`}>
                            {task.status}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">
                          Due {task.dueDate.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Todo & Events */}
          <div className="space-y-6">
            {/* Quick Todo */}
            <div className="card">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Quick Todo</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                    <span className="ml-3 text-sm text-gray-700">Review project proposals</span>
                  </div>
                  <div className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                    <span className="ml-3 text-sm text-gray-700">Update team on progress</span>
                  </div>
                  <div className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" defaultChecked />
                    <span className="ml-3 text-sm text-gray-700 line-through">Prepare presentation</span>
                  </div>
                </div>
                <button className="w-full mt-4 text-sm text-primary-600 hover:text-primary-700 border border-dashed border-primary-300 rounded-lg py-2">
                  + Add new item
                </button>
              </div>
            </div>

            {/* Upcoming Events */}
            <div className="card">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">Upcoming Events</h2>
                  <CalendarIcon className="h-5 w-5 text-gray-400" />
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {mockUpcomingEvents.map((event) => (
                    <div key={event.id} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{event.title}</p>
                        <p className="text-xs text-gray-600">{event.date} at {event.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
                <Link
                  href="/calendar"
                  className="block w-full mt-4 text-center text-sm text-primary-600 hover:text-primary-700 border border-dashed border-primary-300 rounded-lg py-2"
                >
                  View Calendar
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      </DashboardLayout>
    </ProtectedRoute>
  )
}