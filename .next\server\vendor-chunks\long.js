/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/long";
exports.ids = ["vendor-chunks/long"];
exports.modules = {

/***/ "(ssr)/./node_modules/long/umd/index.js":
/*!****************************************!*\
  !*** ./node_modules/long/umd/index.js ***!
  \****************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;// GENERATED FILE. DO NOT EDIT.\n(function (global, factory) {\n  function preferDefault(exports) {\n    return exports.default || exports;\n  }\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n      var exports = {};\n      factory(exports);\n      return preferDefault(exports);\n    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else {}\n})(\n  typeof globalThis !== \"undefined\"\n    ? globalThis\n    : typeof self !== \"undefined\"\n      ? self\n      : this,\n  function (_exports) {\n    \"use strict\";\n\n    Object.defineProperty(_exports, \"__esModule\", {\n      value: true,\n    });\n    _exports.default = void 0;\n    /**\n     * @license\n     * Copyright 2009 The Closure Library Authors\n     * Copyright 2020 Daniel Wirtz / The long.js Authors.\n     *\n     * Licensed under the Apache License, Version 2.0 (the \"License\");\n     * you may not use this file except in compliance with the License.\n     * You may obtain a copy of the License at\n     *\n     *     http://www.apache.org/licenses/LICENSE-2.0\n     *\n     * Unless required by applicable law or agreed to in writing, software\n     * distributed under the License is distributed on an \"AS IS\" BASIS,\n     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n     * See the License for the specific language governing permissions and\n     * limitations under the License.\n     *\n     * SPDX-License-Identifier: Apache-2.0\n     */\n\n    // WebAssembly optimizations to do native i64 multiplication and divide\n    var wasm = null;\n    try {\n      wasm = new WebAssembly.Instance(\n        new WebAssembly.Module(\n          new Uint8Array([\n            // \\0asm\n            0, 97, 115, 109,\n            // version 1\n            1, 0, 0, 0,\n            // section \"type\"\n            1, 13, 2,\n            // 0, () => i32\n            96, 0, 1, 127,\n            // 1, (i32, i32, i32, i32) => i32\n            96, 4, 127, 127, 127, 127, 1, 127,\n            // section \"function\"\n            3, 7, 6,\n            // 0, type 0\n            0,\n            // 1, type 1\n            1,\n            // 2, type 1\n            1,\n            // 3, type 1\n            1,\n            // 4, type 1\n            1,\n            // 5, type 1\n            1,\n            // section \"global\"\n            6, 6, 1,\n            // 0, \"high\", mutable i32\n            127, 1, 65, 0, 11,\n            // section \"export\"\n            7, 50, 6,\n            // 0, \"mul\"\n            3, 109, 117, 108, 0, 1,\n            // 1, \"div_s\"\n            5, 100, 105, 118, 95, 115, 0, 2,\n            // 2, \"div_u\"\n            5, 100, 105, 118, 95, 117, 0, 3,\n            // 3, \"rem_s\"\n            5, 114, 101, 109, 95, 115, 0, 4,\n            // 4, \"rem_u\"\n            5, 114, 101, 109, 95, 117, 0, 5,\n            // 5, \"get_high\"\n            8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0,\n            // section \"code\"\n            10, 191, 1, 6,\n            // 0, \"get_high\"\n            4, 0, 35, 0, 11,\n            // 1, \"mul\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 2, \"div_s\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 3, \"div_u\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 4, \"rem_s\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 5, \"rem_u\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n          ]),\n        ),\n        {},\n      ).exports;\n    } catch {\n      // no wasm support :(\n    }\n\n    /**\n     * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n     *  See the from* functions below for more convenient ways of constructing Longs.\n     * @exports Long\n     * @class A Long class for representing a 64 bit two's-complement integer value.\n     * @param {number} low The low (signed) 32 bits of the long\n     * @param {number} high The high (signed) 32 bits of the long\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @constructor\n     */\n    function Long(low, high, unsigned) {\n      /**\n       * The low 32 bits as a signed value.\n       * @type {number}\n       */\n      this.low = low | 0;\n\n      /**\n       * The high 32 bits as a signed value.\n       * @type {number}\n       */\n      this.high = high | 0;\n\n      /**\n       * Whether unsigned or not.\n       * @type {boolean}\n       */\n      this.unsigned = !!unsigned;\n    }\n\n    // The internal representation of a long is the two given signed, 32-bit values.\n    // We use 32-bit pieces because these are the size of integers on which\n    // Javascript performs bit-operations.  For operations like addition and\n    // multiplication, we split each number into 16 bit pieces, which can easily be\n    // multiplied within Javascript's floating-point representation without overflow\n    // or change in sign.\n    //\n    // In the algorithms below, we frequently reduce the negative case to the\n    // positive case by negating the input(s) and then post-processing the result.\n    // Note that we must ALWAYS check specially whether those values are MIN_VALUE\n    // (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n    // a positive number, it overflows back into a negative).  Not handling this\n    // case would often result in infinite recursion.\n    //\n    // Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n    // methods on which they depend.\n\n    /**\n     * An indicator used to reliably determine if an object is a Long or not.\n     * @type {boolean}\n     * @const\n     * @private\n     */\n    Long.prototype.__isLong__;\n    Object.defineProperty(Long.prototype, \"__isLong__\", {\n      value: true,\n    });\n\n    /**\n     * @function\n     * @param {*} obj Object\n     * @returns {boolean}\n     * @inner\n     */\n    function isLong(obj) {\n      return (obj && obj[\"__isLong__\"]) === true;\n    }\n\n    /**\n     * @function\n     * @param {*} value number\n     * @returns {number}\n     * @inner\n     */\n    function ctz32(value) {\n      var c = Math.clz32(value & -value);\n      return value ? 31 - c : c;\n    }\n\n    /**\n     * Tests if the specified object is a Long.\n     * @function\n     * @param {*} obj Object\n     * @returns {boolean}\n     */\n    Long.isLong = isLong;\n\n    /**\n     * A cache of the Long representations of small integer values.\n     * @type {!Object}\n     * @inner\n     */\n    var INT_CACHE = {};\n\n    /**\n     * A cache of the Long representations of small unsigned integer values.\n     * @type {!Object}\n     * @inner\n     */\n    var UINT_CACHE = {};\n\n    /**\n     * @param {number} value\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromInt(value, unsigned) {\n      var obj, cachedObj, cache;\n      if (unsigned) {\n        value >>>= 0;\n        if ((cache = 0 <= value && value < 256)) {\n          cachedObj = UINT_CACHE[value];\n          if (cachedObj) return cachedObj;\n        }\n        obj = fromBits(value, 0, true);\n        if (cache) UINT_CACHE[value] = obj;\n        return obj;\n      } else {\n        value |= 0;\n        if ((cache = -128 <= value && value < 128)) {\n          cachedObj = INT_CACHE[value];\n          if (cachedObj) return cachedObj;\n        }\n        obj = fromBits(value, value < 0 ? -1 : 0, false);\n        if (cache) INT_CACHE[value] = obj;\n        return obj;\n      }\n    }\n\n    /**\n     * Returns a Long representing the given 32 bit integer value.\n     * @function\n     * @param {number} value The 32 bit integer in question\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromInt = fromInt;\n\n    /**\n     * @param {number} value\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromNumber(value, unsigned) {\n      if (isNaN(value)) return unsigned ? UZERO : ZERO;\n      if (unsigned) {\n        if (value < 0) return UZERO;\n        if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n      } else {\n        if (value <= -TWO_PWR_63_DBL) return MIN_VALUE;\n        if (value + 1 >= TWO_PWR_63_DBL) return MAX_VALUE;\n      }\n      if (value < 0) return fromNumber(-value, unsigned).neg();\n      return fromBits(\n        value % TWO_PWR_32_DBL | 0,\n        (value / TWO_PWR_32_DBL) | 0,\n        unsigned,\n      );\n    }\n\n    /**\n     * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n     * @function\n     * @param {number} value The number in question\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromNumber = fromNumber;\n\n    /**\n     * @param {number} lowBits\n     * @param {number} highBits\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromBits(lowBits, highBits, unsigned) {\n      return new Long(lowBits, highBits, unsigned);\n    }\n\n    /**\n     * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n     *  assumed to use 32 bits.\n     * @function\n     * @param {number} lowBits The low 32 bits\n     * @param {number} highBits The high 32 bits\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromBits = fromBits;\n\n    /**\n     * @function\n     * @param {number} base\n     * @param {number} exponent\n     * @returns {number}\n     * @inner\n     */\n    var pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n\n    /**\n     * @param {string} str\n     * @param {(boolean|number)=} unsigned\n     * @param {number=} radix\n     * @returns {!Long}\n     * @inner\n     */\n    function fromString(str, unsigned, radix) {\n      if (str.length === 0) throw Error(\"empty string\");\n      if (typeof unsigned === \"number\") {\n        // For goog.math.long compatibility\n        radix = unsigned;\n        unsigned = false;\n      } else {\n        unsigned = !!unsigned;\n      }\n      if (\n        str === \"NaN\" ||\n        str === \"Infinity\" ||\n        str === \"+Infinity\" ||\n        str === \"-Infinity\"\n      )\n        return unsigned ? UZERO : ZERO;\n      radix = radix || 10;\n      if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n      var p;\n      if ((p = str.indexOf(\"-\")) > 0) throw Error(\"interior hyphen\");\n      else if (p === 0) {\n        return fromString(str.substring(1), unsigned, radix).neg();\n      }\n\n      // Do several (8) digits each time through the loop, so as to\n      // minimize the calls to the very expensive emulated div.\n      var radixToPower = fromNumber(pow_dbl(radix, 8));\n      var result = ZERO;\n      for (var i = 0; i < str.length; i += 8) {\n        var size = Math.min(8, str.length - i),\n          value = parseInt(str.substring(i, i + size), radix);\n        if (size < 8) {\n          var power = fromNumber(pow_dbl(radix, size));\n          result = result.mul(power).add(fromNumber(value));\n        } else {\n          result = result.mul(radixToPower);\n          result = result.add(fromNumber(value));\n        }\n      }\n      result.unsigned = unsigned;\n      return result;\n    }\n\n    /**\n     * Returns a Long representation of the given string, written using the specified radix.\n     * @function\n     * @param {string} str The textual representation of the Long\n     * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n     * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromString = fromString;\n\n    /**\n     * @function\n     * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromValue(val, unsigned) {\n      if (typeof val === \"number\") return fromNumber(val, unsigned);\n      if (typeof val === \"string\") return fromString(val, unsigned);\n      // Throws for non-objects, converts non-instanceof Long:\n      return fromBits(\n        val.low,\n        val.high,\n        typeof unsigned === \"boolean\" ? unsigned : val.unsigned,\n      );\n    }\n\n    /**\n     * Converts the specified value to a Long using the appropriate from* function for its type.\n     * @function\n     * @param {!Long|number|bigint|string|!{low: number, high: number, unsigned: boolean}} val Value\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long}\n     */\n    Long.fromValue = fromValue;\n\n    // NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n    // no runtime penalty for these.\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_16_DBL = 1 << 16;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_24_DBL = 1 << 24;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n\n    /**\n     * @type {!Long}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var ZERO = fromInt(0);\n\n    /**\n     * Signed zero.\n     * @type {!Long}\n     */\n    Long.ZERO = ZERO;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var UZERO = fromInt(0, true);\n\n    /**\n     * Unsigned zero.\n     * @type {!Long}\n     */\n    Long.UZERO = UZERO;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var ONE = fromInt(1);\n\n    /**\n     * Signed one.\n     * @type {!Long}\n     */\n    Long.ONE = ONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var UONE = fromInt(1, true);\n\n    /**\n     * Unsigned one.\n     * @type {!Long}\n     */\n    Long.UONE = UONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var NEG_ONE = fromInt(-1);\n\n    /**\n     * Signed negative one.\n     * @type {!Long}\n     */\n    Long.NEG_ONE = NEG_ONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MAX_VALUE = fromBits(0xffffffff | 0, 0x7fffffff | 0, false);\n\n    /**\n     * Maximum signed value.\n     * @type {!Long}\n     */\n    Long.MAX_VALUE = MAX_VALUE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MAX_UNSIGNED_VALUE = fromBits(0xffffffff | 0, 0xffffffff | 0, true);\n\n    /**\n     * Maximum unsigned value.\n     * @type {!Long}\n     */\n    Long.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MIN_VALUE = fromBits(0, 0x80000000 | 0, false);\n\n    /**\n     * Minimum signed value.\n     * @type {!Long}\n     */\n    Long.MIN_VALUE = MIN_VALUE;\n\n    /**\n     * @alias Long.prototype\n     * @inner\n     */\n    var LongPrototype = Long.prototype;\n\n    /**\n     * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.toInt = function toInt() {\n      return this.unsigned ? this.low >>> 0 : this.low;\n    };\n\n    /**\n     * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.toNumber = function toNumber() {\n      if (this.unsigned)\n        return (this.high >>> 0) * TWO_PWR_32_DBL + (this.low >>> 0);\n      return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n    };\n\n    /**\n     * Converts the Long to a string written in the specified radix.\n     * @this {!Long}\n     * @param {number=} radix Radix (2-36), defaults to 10\n     * @returns {string}\n     * @override\n     * @throws {RangeError} If `radix` is out of range\n     */\n    LongPrototype.toString = function toString(radix) {\n      radix = radix || 10;\n      if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n      if (this.isZero()) return \"0\";\n      if (this.isNegative()) {\n        // Unsigned Longs are never negative\n        if (this.eq(MIN_VALUE)) {\n          // We need to change the Long value before it can be negated, so we remove\n          // the bottom-most digit in this base and then recurse to do the rest.\n          var radixLong = fromNumber(radix),\n            div = this.div(radixLong),\n            rem1 = div.mul(radixLong).sub(this);\n          return div.toString(radix) + rem1.toInt().toString(radix);\n        } else return \"-\" + this.neg().toString(radix);\n      }\n\n      // Do several (6) digits each time through the loop, so as to\n      // minimize the calls to the very expensive emulated div.\n      var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n        rem = this;\n      var result = \"\";\n      while (true) {\n        var remDiv = rem.div(radixToPower),\n          intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n          digits = intval.toString(radix);\n        rem = remDiv;\n        if (rem.isZero()) return digits + result;\n        else {\n          while (digits.length < 6) digits = \"0\" + digits;\n          result = \"\" + digits + result;\n        }\n      }\n    };\n\n    /**\n     * Gets the high 32 bits as a signed integer.\n     * @this {!Long}\n     * @returns {number} Signed high bits\n     */\n    LongPrototype.getHighBits = function getHighBits() {\n      return this.high;\n    };\n\n    /**\n     * Gets the high 32 bits as an unsigned integer.\n     * @this {!Long}\n     * @returns {number} Unsigned high bits\n     */\n    LongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n      return this.high >>> 0;\n    };\n\n    /**\n     * Gets the low 32 bits as a signed integer.\n     * @this {!Long}\n     * @returns {number} Signed low bits\n     */\n    LongPrototype.getLowBits = function getLowBits() {\n      return this.low;\n    };\n\n    /**\n     * Gets the low 32 bits as an unsigned integer.\n     * @this {!Long}\n     * @returns {number} Unsigned low bits\n     */\n    LongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n      return this.low >>> 0;\n    };\n\n    /**\n     * Gets the number of bits needed to represent the absolute value of this Long.\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n      if (this.isNegative())\n        // Unsigned Longs are never negative\n        return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n      var val = this.high != 0 ? this.high : this.low;\n      for (var bit = 31; bit > 0; bit--) if ((val & (1 << bit)) != 0) break;\n      return this.high != 0 ? bit + 33 : bit + 1;\n    };\n\n    /**\n     * Tests if this Long can be safely represented as a JavaScript number.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isSafeInteger = function isSafeInteger() {\n      // 2^53-1 is the maximum safe value\n      var top11Bits = this.high >> 21;\n      // [0, 2^53-1]\n      if (!top11Bits) return true;\n      // > 2^53-1\n      if (this.unsigned) return false;\n      // [-2^53, -1] except -2^53\n      return top11Bits === -1 && !(this.low === 0 && this.high === -0x200000);\n    };\n\n    /**\n     * Tests if this Long's value equals zero.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isZero = function isZero() {\n      return this.high === 0 && this.low === 0;\n    };\n\n    /**\n     * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n     * @returns {boolean}\n     */\n    LongPrototype.eqz = LongPrototype.isZero;\n\n    /**\n     * Tests if this Long's value is negative.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isNegative = function isNegative() {\n      return !this.unsigned && this.high < 0;\n    };\n\n    /**\n     * Tests if this Long's value is positive or zero.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isPositive = function isPositive() {\n      return this.unsigned || this.high >= 0;\n    };\n\n    /**\n     * Tests if this Long's value is odd.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isOdd = function isOdd() {\n      return (this.low & 1) === 1;\n    };\n\n    /**\n     * Tests if this Long's value is even.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isEven = function isEven() {\n      return (this.low & 1) === 0;\n    };\n\n    /**\n     * Tests if this Long's value equals the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.equals = function equals(other) {\n      if (!isLong(other)) other = fromValue(other);\n      if (\n        this.unsigned !== other.unsigned &&\n        this.high >>> 31 === 1 &&\n        other.high >>> 31 === 1\n      )\n        return false;\n      return this.high === other.high && this.low === other.low;\n    };\n\n    /**\n     * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.eq = LongPrototype.equals;\n\n    /**\n     * Tests if this Long's value differs from the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.notEquals = function notEquals(other) {\n      return !this.eq(/* validates */ other);\n    };\n\n    /**\n     * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.neq = LongPrototype.notEquals;\n\n    /**\n     * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.ne = LongPrototype.notEquals;\n\n    /**\n     * Tests if this Long's value is less than the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lessThan = function lessThan(other) {\n      return this.comp(/* validates */ other) < 0;\n    };\n\n    /**\n     * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lt = LongPrototype.lessThan;\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n      return this.comp(/* validates */ other) <= 0;\n    };\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lte = LongPrototype.lessThanOrEqual;\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.le = LongPrototype.lessThanOrEqual;\n\n    /**\n     * Tests if this Long's value is greater than the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.greaterThan = function greaterThan(other) {\n      return this.comp(/* validates */ other) > 0;\n    };\n\n    /**\n     * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.gt = LongPrototype.greaterThan;\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n      return this.comp(/* validates */ other) >= 0;\n    };\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.gte = LongPrototype.greaterThanOrEqual;\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.ge = LongPrototype.greaterThanOrEqual;\n\n    /**\n     * Compares this Long's value with the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n     *  if the given one is greater\n     */\n    LongPrototype.compare = function compare(other) {\n      if (!isLong(other)) other = fromValue(other);\n      if (this.eq(other)) return 0;\n      var thisNeg = this.isNegative(),\n        otherNeg = other.isNegative();\n      if (thisNeg && !otherNeg) return -1;\n      if (!thisNeg && otherNeg) return 1;\n      // At this point the sign bits are the same\n      if (!this.unsigned) return this.sub(other).isNegative() ? -1 : 1;\n      // Both are positive if at least one is unsigned\n      return other.high >>> 0 > this.high >>> 0 ||\n        (other.high === this.high && other.low >>> 0 > this.low >>> 0)\n        ? -1\n        : 1;\n    };\n\n    /**\n     * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n     *  if the given one is greater\n     */\n    LongPrototype.comp = LongPrototype.compare;\n\n    /**\n     * Negates this Long's value.\n     * @this {!Long}\n     * @returns {!Long} Negated Long\n     */\n    LongPrototype.negate = function negate() {\n      if (!this.unsigned && this.eq(MIN_VALUE)) return MIN_VALUE;\n      return this.not().add(ONE);\n    };\n\n    /**\n     * Negates this Long's value. This is an alias of {@link Long#negate}.\n     * @function\n     * @returns {!Long} Negated Long\n     */\n    LongPrototype.neg = LongPrototype.negate;\n\n    /**\n     * Returns the sum of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} addend Addend\n     * @returns {!Long} Sum\n     */\n    LongPrototype.add = function add(addend) {\n      if (!isLong(addend)) addend = fromValue(addend);\n\n      // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n\n      var a48 = this.high >>> 16;\n      var a32 = this.high & 0xffff;\n      var a16 = this.low >>> 16;\n      var a00 = this.low & 0xffff;\n      var b48 = addend.high >>> 16;\n      var b32 = addend.high & 0xffff;\n      var b16 = addend.low >>> 16;\n      var b00 = addend.low & 0xffff;\n      var c48 = 0,\n        c32 = 0,\n        c16 = 0,\n        c00 = 0;\n      c00 += a00 + b00;\n      c16 += c00 >>> 16;\n      c00 &= 0xffff;\n      c16 += a16 + b16;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c32 += a32 + b32;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c48 += a48 + b48;\n      c48 &= 0xffff;\n      return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n    };\n\n    /**\n     * Returns the difference of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} subtrahend Subtrahend\n     * @returns {!Long} Difference\n     */\n    LongPrototype.subtract = function subtract(subtrahend) {\n      if (!isLong(subtrahend)) subtrahend = fromValue(subtrahend);\n      return this.add(subtrahend.neg());\n    };\n\n    /**\n     * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n     * @function\n     * @param {!Long|number|bigint|string} subtrahend Subtrahend\n     * @returns {!Long} Difference\n     */\n    LongPrototype.sub = LongPrototype.subtract;\n\n    /**\n     * Returns the product of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} multiplier Multiplier\n     * @returns {!Long} Product\n     */\n    LongPrototype.multiply = function multiply(multiplier) {\n      if (this.isZero()) return this;\n      if (!isLong(multiplier)) multiplier = fromValue(multiplier);\n\n      // use wasm support if present\n      if (wasm) {\n        var low = wasm[\"mul\"](\n          this.low,\n          this.high,\n          multiplier.low,\n          multiplier.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      if (multiplier.isZero()) return this.unsigned ? UZERO : ZERO;\n      if (this.eq(MIN_VALUE)) return multiplier.isOdd() ? MIN_VALUE : ZERO;\n      if (multiplier.eq(MIN_VALUE)) return this.isOdd() ? MIN_VALUE : ZERO;\n      if (this.isNegative()) {\n        if (multiplier.isNegative()) return this.neg().mul(multiplier.neg());\n        else return this.neg().mul(multiplier).neg();\n      } else if (multiplier.isNegative())\n        return this.mul(multiplier.neg()).neg();\n\n      // If both longs are small, use float multiplication\n      if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\n        return fromNumber(\n          this.toNumber() * multiplier.toNumber(),\n          this.unsigned,\n        );\n\n      // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n      // We can skip products that would overflow.\n\n      var a48 = this.high >>> 16;\n      var a32 = this.high & 0xffff;\n      var a16 = this.low >>> 16;\n      var a00 = this.low & 0xffff;\n      var b48 = multiplier.high >>> 16;\n      var b32 = multiplier.high & 0xffff;\n      var b16 = multiplier.low >>> 16;\n      var b00 = multiplier.low & 0xffff;\n      var c48 = 0,\n        c32 = 0,\n        c16 = 0,\n        c00 = 0;\n      c00 += a00 * b00;\n      c16 += c00 >>> 16;\n      c00 &= 0xffff;\n      c16 += a16 * b00;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c16 += a00 * b16;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c32 += a32 * b00;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c32 += a16 * b16;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c32 += a00 * b32;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n      c48 &= 0xffff;\n      return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n    };\n\n    /**\n     * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n     * @function\n     * @param {!Long|number|bigint|string} multiplier Multiplier\n     * @returns {!Long} Product\n     */\n    LongPrototype.mul = LongPrototype.multiply;\n\n    /**\n     * Returns this Long divided by the specified. The result is signed if this Long is signed or\n     *  unsigned if this Long is unsigned.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Quotient\n     */\n    LongPrototype.divide = function divide(divisor) {\n      if (!isLong(divisor)) divisor = fromValue(divisor);\n      if (divisor.isZero()) throw Error(\"division by zero\");\n\n      // use wasm support if present\n      if (wasm) {\n        // guard against signed division overflow: the largest\n        // negative number / -1 would be 1 larger than the largest\n        // positive number, due to two's complement.\n        if (\n          !this.unsigned &&\n          this.high === -0x80000000 &&\n          divisor.low === -1 &&\n          divisor.high === -1\n        ) {\n          // be consistent with non-wasm code path\n          return this;\n        }\n        var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(\n          this.low,\n          this.high,\n          divisor.low,\n          divisor.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      if (this.isZero()) return this.unsigned ? UZERO : ZERO;\n      var approx, rem, res;\n      if (!this.unsigned) {\n        // This section is only relevant for signed longs and is derived from the\n        // closure library as a whole.\n        if (this.eq(MIN_VALUE)) {\n          if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\n            return MIN_VALUE; // recall that -MIN_VALUE == MIN_VALUE\n          else if (divisor.eq(MIN_VALUE)) return ONE;\n          else {\n            // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n            var halfThis = this.shr(1);\n            approx = halfThis.div(divisor).shl(1);\n            if (approx.eq(ZERO)) {\n              return divisor.isNegative() ? ONE : NEG_ONE;\n            } else {\n              rem = this.sub(divisor.mul(approx));\n              res = approx.add(rem.div(divisor));\n              return res;\n            }\n          }\n        } else if (divisor.eq(MIN_VALUE)) return this.unsigned ? UZERO : ZERO;\n        if (this.isNegative()) {\n          if (divisor.isNegative()) return this.neg().div(divisor.neg());\n          return this.neg().div(divisor).neg();\n        } else if (divisor.isNegative()) return this.div(divisor.neg()).neg();\n        res = ZERO;\n      } else {\n        // The algorithm below has not been made for unsigned longs. It's therefore\n        // required to take special care of the MSB prior to running it.\n        if (!divisor.unsigned) divisor = divisor.toUnsigned();\n        if (divisor.gt(this)) return UZERO;\n        if (divisor.gt(this.shru(1)))\n          // 15 >>> 1 = 7 ; with divisor = 8 ; true\n          return UONE;\n        res = UZERO;\n      }\n\n      // Repeat the following until the remainder is less than other:  find a\n      // floating-point that approximates remainder / other *from below*, add this\n      // into the result, and subtract it from the remainder.  It is critical that\n      // the approximate value is less than or equal to the real value so that the\n      // remainder never becomes negative.\n      rem = this;\n      while (rem.gte(divisor)) {\n        // Approximate the result of division. This may be a little greater or\n        // smaller than the actual value.\n        approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n\n        // We will tweak the approximate result by changing it in the 48-th digit or\n        // the smallest non-fractional digit, whichever is larger.\n        var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n          delta = log2 <= 48 ? 1 : pow_dbl(2, log2 - 48),\n          // Decrease the approximation until it is smaller than the remainder.  Note\n          // that if it is too large, the product overflows and is negative.\n          approxRes = fromNumber(approx),\n          approxRem = approxRes.mul(divisor);\n        while (approxRem.isNegative() || approxRem.gt(rem)) {\n          approx -= delta;\n          approxRes = fromNumber(approx, this.unsigned);\n          approxRem = approxRes.mul(divisor);\n        }\n\n        // We know the answer can't be zero... and actually, zero would cause\n        // infinite recursion since we would make no progress.\n        if (approxRes.isZero()) approxRes = ONE;\n        res = res.add(approxRes);\n        rem = rem.sub(approxRem);\n      }\n      return res;\n    };\n\n    /**\n     * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Quotient\n     */\n    LongPrototype.div = LongPrototype.divide;\n\n    /**\n     * Returns this Long modulo the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.modulo = function modulo(divisor) {\n      if (!isLong(divisor)) divisor = fromValue(divisor);\n\n      // use wasm support if present\n      if (wasm) {\n        var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(\n          this.low,\n          this.high,\n          divisor.low,\n          divisor.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      return this.sub(this.div(divisor).mul(divisor));\n    };\n\n    /**\n     * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.mod = LongPrototype.modulo;\n\n    /**\n     * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.rem = LongPrototype.modulo;\n\n    /**\n     * Returns the bitwise NOT of this Long.\n     * @this {!Long}\n     * @returns {!Long}\n     */\n    LongPrototype.not = function not() {\n      return fromBits(~this.low, ~this.high, this.unsigned);\n    };\n\n    /**\n     * Returns count leading zeros of this Long.\n     * @this {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.countLeadingZeros = function countLeadingZeros() {\n      return this.high ? Math.clz32(this.high) : Math.clz32(this.low) + 32;\n    };\n\n    /**\n     * Returns count leading zeros. This is an alias of {@link Long#countLeadingZeros}.\n     * @function\n     * @param {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.clz = LongPrototype.countLeadingZeros;\n\n    /**\n     * Returns count trailing zeros of this Long.\n     * @this {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.countTrailingZeros = function countTrailingZeros() {\n      return this.low ? ctz32(this.low) : ctz32(this.high) + 32;\n    };\n\n    /**\n     * Returns count trailing zeros. This is an alias of {@link Long#countTrailingZeros}.\n     * @function\n     * @param {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.ctz = LongPrototype.countTrailingZeros;\n\n    /**\n     * Returns the bitwise AND of this Long and the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.and = function and(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low & other.low,\n        this.high & other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns the bitwise OR of this Long and the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.or = function or(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low | other.low,\n        this.high | other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns the bitwise XOR of this Long and the given one.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.xor = function xor(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low ^ other.low,\n        this.high ^ other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns this Long with bits shifted to the left by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftLeft = function shiftLeft(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      else if (numBits < 32)\n        return fromBits(\n          this.low << numBits,\n          (this.high << numBits) | (this.low >>> (32 - numBits)),\n          this.unsigned,\n        );\n      else return fromBits(0, this.low << (numBits - 32), this.unsigned);\n    };\n\n    /**\n     * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shl = LongPrototype.shiftLeft;\n\n    /**\n     * Returns this Long with bits arithmetically shifted to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftRight = function shiftRight(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      else if (numBits < 32)\n        return fromBits(\n          (this.low >>> numBits) | (this.high << (32 - numBits)),\n          this.high >> numBits,\n          this.unsigned,\n        );\n      else\n        return fromBits(\n          this.high >> (numBits - 32),\n          this.high >= 0 ? 0 : -1,\n          this.unsigned,\n        );\n    };\n\n    /**\n     * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shr = LongPrototype.shiftRight;\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits < 32)\n        return fromBits(\n          (this.low >>> numBits) | (this.high << (32 - numBits)),\n          this.high >>> numBits,\n          this.unsigned,\n        );\n      if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n      return fromBits(this.high >>> (numBits - 32), 0, this.unsigned);\n    };\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shru = LongPrototype.shiftRightUnsigned;\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n\n    /**\n     * Returns this Long with bits rotated to the left by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotateLeft = function rotateLeft(numBits) {\n      var b;\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n      if (numBits < 32) {\n        b = 32 - numBits;\n        return fromBits(\n          (this.low << numBits) | (this.high >>> b),\n          (this.high << numBits) | (this.low >>> b),\n          this.unsigned,\n        );\n      }\n      numBits -= 32;\n      b = 32 - numBits;\n      return fromBits(\n        (this.high << numBits) | (this.low >>> b),\n        (this.low << numBits) | (this.high >>> b),\n        this.unsigned,\n      );\n    };\n    /**\n     * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotl = LongPrototype.rotateLeft;\n\n    /**\n     * Returns this Long with bits rotated to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotateRight = function rotateRight(numBits) {\n      var b;\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n      if (numBits < 32) {\n        b = 32 - numBits;\n        return fromBits(\n          (this.high << b) | (this.low >>> numBits),\n          (this.low << b) | (this.high >>> numBits),\n          this.unsigned,\n        );\n      }\n      numBits -= 32;\n      b = 32 - numBits;\n      return fromBits(\n        (this.low << b) | (this.high >>> numBits),\n        (this.high << b) | (this.low >>> numBits),\n        this.unsigned,\n      );\n    };\n    /**\n     * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotr = LongPrototype.rotateRight;\n\n    /**\n     * Converts this Long to signed.\n     * @this {!Long}\n     * @returns {!Long} Signed long\n     */\n    LongPrototype.toSigned = function toSigned() {\n      if (!this.unsigned) return this;\n      return fromBits(this.low, this.high, false);\n    };\n\n    /**\n     * Converts this Long to unsigned.\n     * @this {!Long}\n     * @returns {!Long} Unsigned long\n     */\n    LongPrototype.toUnsigned = function toUnsigned() {\n      if (this.unsigned) return this;\n      return fromBits(this.low, this.high, true);\n    };\n\n    /**\n     * Converts this Long to its byte representation.\n     * @param {boolean=} le Whether little or big endian, defaults to big endian\n     * @this {!Long}\n     * @returns {!Array.<number>} Byte representation\n     */\n    LongPrototype.toBytes = function toBytes(le) {\n      return le ? this.toBytesLE() : this.toBytesBE();\n    };\n\n    /**\n     * Converts this Long to its little endian byte representation.\n     * @this {!Long}\n     * @returns {!Array.<number>} Little endian byte representation\n     */\n    LongPrototype.toBytesLE = function toBytesLE() {\n      var hi = this.high,\n        lo = this.low;\n      return [\n        lo & 0xff,\n        (lo >>> 8) & 0xff,\n        (lo >>> 16) & 0xff,\n        lo >>> 24,\n        hi & 0xff,\n        (hi >>> 8) & 0xff,\n        (hi >>> 16) & 0xff,\n        hi >>> 24,\n      ];\n    };\n\n    /**\n     * Converts this Long to its big endian byte representation.\n     * @this {!Long}\n     * @returns {!Array.<number>} Big endian byte representation\n     */\n    LongPrototype.toBytesBE = function toBytesBE() {\n      var hi = this.high,\n        lo = this.low;\n      return [\n        hi >>> 24,\n        (hi >>> 16) & 0xff,\n        (hi >>> 8) & 0xff,\n        hi & 0xff,\n        lo >>> 24,\n        (lo >>> 16) & 0xff,\n        (lo >>> 8) & 0xff,\n        lo & 0xff,\n      ];\n    };\n\n    /**\n     * Creates a Long from its byte representation.\n     * @param {!Array.<number>} bytes Byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @param {boolean=} le Whether little or big endian, defaults to big endian\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytes = function fromBytes(bytes, unsigned, le) {\n      return le\n        ? Long.fromBytesLE(bytes, unsigned)\n        : Long.fromBytesBE(bytes, unsigned);\n    };\n\n    /**\n     * Creates a Long from its little endian byte representation.\n     * @param {!Array.<number>} bytes Little endian byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n      return new Long(\n        bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24),\n        bytes[4] | (bytes[5] << 8) | (bytes[6] << 16) | (bytes[7] << 24),\n        unsigned,\n      );\n    };\n\n    /**\n     * Creates a Long from its big endian byte representation.\n     * @param {!Array.<number>} bytes Big endian byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n      return new Long(\n        (bytes[4] << 24) | (bytes[5] << 16) | (bytes[6] << 8) | bytes[7],\n        (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3],\n        unsigned,\n      );\n    };\n\n    // Support conversion to/from BigInt where available\n    if (typeof BigInt === \"function\") {\n      /**\n       * Returns a Long representing the given big integer.\n       * @function\n       * @param {number} value The big integer value\n       * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n       * @returns {!Long} The corresponding Long value\n       */\n      Long.fromBigInt = function fromBigInt(value, unsigned) {\n        var lowBits = Number(BigInt.asIntN(32, value));\n        var highBits = Number(BigInt.asIntN(32, value >> BigInt(32)));\n        return fromBits(lowBits, highBits, unsigned);\n      };\n\n      // Override\n      Long.fromValue = function fromValueWithBigInt(value, unsigned) {\n        if (typeof value === \"bigint\") return Long.fromBigInt(value, unsigned);\n        return fromValue(value, unsigned);\n      };\n\n      /**\n       * Converts the Long to its big integer representation.\n       * @this {!Long}\n       * @returns {bigint}\n       */\n      LongPrototype.toBigInt = function toBigInt() {\n        var lowBigInt = BigInt(this.low >>> 0);\n        var highBigInt = BigInt(this.unsigned ? this.high >>> 0 : this.high);\n        return (highBigInt << BigInt(32)) | lowBigInt;\n      };\n    }\n    var _default = (_exports.default = Long);\n  },\n);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/long/umd/index.js\n");

/***/ })

};
;