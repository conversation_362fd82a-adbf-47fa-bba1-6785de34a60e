[debug] [2025-07-18T12:48:10.394Z] ----------------------------------------------------------------------
[debug] [2025-07-18T12:48:10.398Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only firestore:rules --debug
[debug] [2025-07-18T12:48:10.399Z] CLI Version:   14.11.0
[debug] [2025-07-18T12:48:10.399Z] Platform:      win32
[debug] [2025-07-18T12:48:10.399Z] Node Version:  v22.16.0
[debug] [2025-07-18T12:48:10.399Z] Time:          Fri Jul 18 2025 15:48:10 GMT+0300 (Eastern European Summer Time)
[debug] [2025-07-18T12:48:10.399Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-18T12:48:10.667Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-18T12:48:10.668Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-18T12:48:10.668Z] [iam] checking project zatconss for permissions ["datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get"]
[debug] [2025-07-18T12:48:10.670Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:10.670Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:10.672Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/zatconss:testIamPermissions [none]
[debug] [2025-07-18T12:48:10.672Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/zatconss:testIamPermissions x-goog-quota-user=projects/zatconss
[debug] [2025-07-18T12:48:10.672Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/zatconss:testIamPermissions {"permissions":["datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get"]}
[debug] [2025-07-18T12:48:11.524Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/zatconss:testIamPermissions 200
[debug] [2025-07-18T12:48:11.525Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/zatconss:testIamPermissions {"permissions":["datastore.indexes.create","datastore.indexes.delete","datastore.indexes.list","datastore.indexes.update","firebase.projects.get"]}
[info] 
[info] === Deploying to 'zatconss'...
[info] 
[info] i  deploying firestore 
[info] i  firestore: reading indexes from firestore.indexes.json... 
[info] i  cloud.firestore: checking firestore.rules for compilation errors... 
[debug] [2025-07-18T12:48:11.531Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:11.532Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:11.532Z] >>> [apiv2][query] POST https://firebaserules.googleapis.com/v1/projects/zatconss:test [none]
[debug] [2025-07-18T12:48:11.533Z] >>> [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/zatconss:test [omitted]
[debug] [2025-07-18T12:48:11.950Z] <<< [apiv2][status] POST https://firebaserules.googleapis.com/v1/projects/zatconss:test 200
[debug] [2025-07-18T12:48:11.950Z] <<< [apiv2][body] POST https://firebaserules.googleapis.com/v1/projects/zatconss:test {}
[info] +  cloud.firestore: rules file firestore.rules compiled successfully 
[debug] [2025-07-18T12:48:11.952Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:11.952Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:11.952Z] >>> [apiv2][query] GET https://firestore.googleapis.com/v1/projects/zatconss/databases/(default) [none]
[debug] [2025-07-18T12:48:12.421Z] <<< [apiv2][status] GET https://firestore.googleapis.com/v1/projects/zatconss/databases/(default) 200
[debug] [2025-07-18T12:48:12.421Z] <<< [apiv2][body] GET https://firestore.googleapis.com/v1/projects/zatconss/databases/(default) {"name":"projects/zatconss/databases/(default)","uid":"bcff40b5-**************-d3c2121ab858","createTime":"2024-05-31T12:41:03.515611Z","updateTime":"2024-05-31T12:41:03.515611Z","locationId":"europe-west2","type":"FIRESTORE_NATIVE","concurrencyMode":"PESSIMISTIC","versionRetentionPeriod":"3600s","earliestVersionTime":"2025-07-18T11:48:13.203791Z","appEngineIntegrationMode":"DISABLED","keyPrefix":"g","pointInTimeRecoveryEnablement":"POINT_IN_TIME_RECOVERY_DISABLED","deleteProtectionState":"DELETE_PROTECTION_DISABLED","databaseEdition":"STANDARD","freeTier":true,"etag":"IKCr/M+4xo4DMKSykPnjuY4D"}
[debug] [2025-07-18T12:48:12.424Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:12.425Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:12.425Z] >>> [apiv2][query] GET https://firebaserules.googleapis.com/v1/projects/zatconss/releases pageSize=10&pageToken=
[debug] [2025-07-18T12:48:12.650Z] <<< [apiv2][status] GET https://firebaserules.googleapis.com/v1/projects/zatconss/releases 200
[debug] [2025-07-18T12:48:12.651Z] <<< [apiv2][body] GET https://firebaserules.googleapis.com/v1/projects/zatconss/releases {"releases":[{"name":"projects/zatconss/releases/cloud.firestore","rulesetName":"projects/zatconss/rulesets/30ea628c-87b3-4b3b-8307-48880215729b","createTime":"2024-05-31T12:41:14.483986Z","updateTime":"2025-07-18T12:47:55.195589Z"}]}
[debug] [2025-07-18T12:48:12.652Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:12.652Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:12.652Z] >>> [apiv2][query] GET https://firebaserules.googleapis.com/v1/projects/zatconss/rulesets/30ea628c-87b3-4b3b-8307-48880215729b [none]
[debug] [2025-07-18T12:48:13.316Z] <<< [apiv2][status] GET https://firebaserules.googleapis.com/v1/projects/zatconss/rulesets/30ea628c-87b3-4b3b-8307-48880215729b 200
[debug] [2025-07-18T12:48:13.317Z] <<< [apiv2][body] GET https://firebaserules.googleapis.com/v1/projects/zatconss/rulesets/30ea628c-87b3-4b3b-8307-48880215729b [omitted]
[info] i  firestore: latest version of firestore.rules already up to date, skipping upload... 
[debug] [2025-07-18T12:48:13.318Z] [rules] releasing cloud.firestore/(default) with ruleset projects/zatconss/rulesets/30ea628c-87b3-4b3b-8307-48880215729b
[debug] [2025-07-18T12:48:13.319Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:13.319Z] Checked if tokens are valid: true, expires at: 1752846407204
[debug] [2025-07-18T12:48:13.319Z] >>> [apiv2][query] PATCH https://firebaserules.googleapis.com/v1/projects/zatconss/releases/cloud.firestore/(default) [none]
[debug] [2025-07-18T12:48:13.320Z] >>> [apiv2][body] PATCH https://firebaserules.googleapis.com/v1/projects/zatconss/releases/cloud.firestore/(default) {"release":{"name":"projects/zatconss/releases/cloud.firestore/(default)","rulesetName":"projects/zatconss/rulesets/30ea628c-87b3-4b3b-8307-48880215729b"}}
[debug] [2025-07-18T12:48:14.173Z] <<< [apiv2][status] PATCH https://firebaserules.googleapis.com/v1/projects/zatconss/releases/cloud.firestore/(default) 200
