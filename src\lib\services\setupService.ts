import { 
  collection, 
  getDocs, 
  doc, 
  setDoc, 
  getDoc,
  Timestamp,
  query,
  limit
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { User, Organization } from '@/lib/types'

export class SetupService {
  /**
   * Check if this is the first user (admin) in the system
   */
  static async isFirstUser(): Promise<boolean> {
    try {
      const usersQuery = query(collection(db, 'users'), limit(1))
      const snapshot = await getDocs(usersQuery)
      return snapshot.empty
    } catch (error) {
      console.error('Error checking if first user:', error)
      return false
    }
  }

  /**
   * Create the admin user and default organization
   */
  static async createAdminUser(firebaseUser: any, displayName: string): Promise<{ user: User; organization: Organization }> {
    try {
      // Create admin user document - filter out undefined values
      const adminUserData: any = {
        id: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: displayName || firebaseUser.displayName || 'Admin',
        role: 'admin', // First user is always admin
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        preferences: {
          theme: 'light',
          notifications: true
        }
      }

      // Only add photoURL if it exists
      if (firebaseUser.photoURL) {
        adminUserData.photoURL = firebaseUser.photoURL
      }

      const adminUser: User = adminUserData

      // Create default organization
      const defaultOrganization: Organization = {
        id: 'default-org',
        name: `${adminUser.displayName}'s Organization`,
        description: 'Default organization for the admin user',
        ownerId: firebaseUser.uid,
        members: {
          [firebaseUser.uid]: {
            role: 'admin',
            joinedAt: Timestamp.now()
          }
        },
        settings: {
          aiModels: {
            'anthropic/claude-3-haiku': {
              enabled: true,
              displayName: 'Claude 3 Haiku (Fast)',
              provider: 'openrouter'
            },
            'anthropic/claude-3-sonnet': {
              enabled: true,
              displayName: 'Claude 3 Sonnet (Balanced)',
              provider: 'openrouter'
            },
            'openai/gpt-4': {
              enabled: true,
              displayName: 'GPT-4 (Advanced)',
              provider: 'openrouter'
            },
            'openai/gpt-3.5-turbo': {
              enabled: true,
              displayName: 'GPT-3.5 Turbo (Fast)',
              provider: 'openrouter'
            }
          },
          mcpEnabled: false // Disabled by default for security
        },
        apiKeys: {
          openrouter: [] // Will be configured in admin panel
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }

      // Save both documents
      await Promise.all([
        setDoc(doc(db, 'users', firebaseUser.uid), adminUser),
        setDoc(doc(db, 'organizations', 'default-org'), defaultOrganization)
      ])

      return { user: adminUser, organization: defaultOrganization }
    } catch (error: any) {
      console.error('Error creating admin user:', error)

      // Provide more specific error messages
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied. Please deploy Firestore security rules first. See FIRESTORE_SETUP.md for instructions.')
      } else if (error.code === 'unavailable') {
        throw new Error('Firestore is currently unavailable. Please try again later.')
      } else if (error.code === 'failed-precondition') {
        throw new Error('Firestore operation failed. Please check your Firebase configuration.')
      }

      throw new Error(`Failed to create admin user: ${error.message}`)
    }
  }

  /**
   * Check if the system has been initialized
   */
  static async isSystemInitialized(): Promise<boolean> {
    try {
      const orgDoc = await getDoc(doc(db, 'organizations', 'default-org'))
      return orgDoc.exists()
    } catch (error) {
      console.error('Error checking system initialization:', error)
      return false
    }
  }

  /**
   * Get the default organization
   */
  static async getDefaultOrganization(): Promise<Organization | null> {
    try {
      const orgDoc = await getDoc(doc(db, 'organizations', 'default-org'))
      if (orgDoc.exists()) {
        return { id: orgDoc.id, ...orgDoc.data() } as Organization
      }
      return null
    } catch (error) {
      console.error('Error getting default organization:', error)
      return null
    }
  }

  /**
   * Create a regular user (non-admin)
   */
  static async createRegularUser(firebaseUser: any, displayName: string): Promise<User> {
    try {
      // Create user document - filter out undefined values
      const userData: any = {
        id: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: displayName || firebaseUser.displayName || '',
        role: 'user', // Regular users are not admin
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        preferences: {
          theme: 'light',
          notifications: true
        }
      }

      // Only add photoURL if it exists
      if (firebaseUser.photoURL) {
        userData.photoURL = firebaseUser.photoURL
      }

      const user: User = userData

      await setDoc(doc(db, 'users', firebaseUser.uid), userData)
      return user
    } catch (error: any) {
      console.error('Error creating regular user:', error)

      // Provide more specific error messages
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied. Please deploy Firestore security rules first. See FIRESTORE_SETUP.md for instructions.')
      } else if (error.code === 'unavailable') {
        throw new Error('Firestore is currently unavailable. Please try again later.')
      } else if (error.code === 'failed-precondition') {
        throw new Error('Firestore operation failed. Please check your Firebase configuration.')
      }

      throw new Error(`Failed to create user: ${error.message}`)
    }
  }
}