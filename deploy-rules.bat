@echo off
echo Checking Firebase CLI...

firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI not found. Installing...
    echo Please install Firebase CLI first:
    echo npm install -g firebase-tools
    echo.
    echo Then run: firebase login
    echo Then run: firebase init firestore
    echo.
    pause
    exit /b 1
)

echo Deploying Firestore security rules...
firebase deploy --only firestore:rules

if %errorlevel% equ 0 (
    echo.
    echo Rules deployed successfully!
    echo You can now test user registration and admin setup.
) else (
    echo.
    echo Deployment failed. Please check your Firebase configuration.
    echo Make sure you're logged in: firebase login
    echo Make sure project is initialized: firebase init firestore
)

pause
