{"indexes": [{"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "projectId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "assignedTo", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "activityLogs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "organizationId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "chatSessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}