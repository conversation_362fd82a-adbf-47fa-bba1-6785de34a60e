# Firestore Setup Guide

## Quick Fix for "Missing or insufficient permissions" Error

The error you're seeing is because Firestore security rules haven't been deployed yet. Here's how to fix it:

### Option 1: Deploy Rules (Recommended)

1. **Install Firebase CLI** (if not already installed):
   ```bash
   npm install -g firebase-tools
   ```

2. **Login to Firebase**:
   ```bash
   firebase login
   ```

3. **Initialize Firestore** (if not already done):
   ```bash
   firebase init firestore
   ```
   - Select your Firebase project
   - Accept default rules file (firestore.rules)
   - Accept default indexes file (firestore.indexes.json)

4. **Deploy the rules**:
   ```bash
   firebase deploy --only firestore:rules
   ```
   
   Or run the provided script:
   ```bash
   # Windows
   deploy-rules.bat
   
   # Linux/Mac
   ./deploy-rules.sh
   ```

### Option 2: Temporary Fix via Firebase Console

If you can't use Firebase CLI right now:

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project (`zatconss`)
3. Go to **Firestore Database** → **Rules**
4. Replace the existing rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY: Allow all operations for development/testing
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

5. Click **Publish**

## Testing After Setup

Once rules are deployed, test:

1. **Admin Setup**: Visit `/admin-setup`
   - Password: `yAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ`
   - Key: `f62c8e2bf4952eebe5c4c`

2. **User Registration**: Visit `/auth/register`

3. **User Login**: Visit `/auth/login`

## Security Note

The current rules allow all operations for development. For production, you should use more restrictive rules. The project includes proper security rules in `firestore.rules` that you can uncomment and customize.

## Troubleshooting

### Error: "Firebase project not found"
- Run `firebase projects:list` to see available projects
- Run `firebase use <project-id>` to select the correct project

### Error: "Permission denied"
- Make sure you're logged in: `firebase login`
- Make sure you have admin access to the Firebase project

### Error: "Rules compilation failed"
- Check the syntax in `firestore.rules`
- The current rules are very simple and should work

## Production Security Rules

For production, replace the open rules with proper security rules that:
- Allow users to read/write only their own data
- Allow admins to manage all data
- Implement proper organization and project-based access control

The project includes a template for production rules in the `firestore.rules` file (commented out).
