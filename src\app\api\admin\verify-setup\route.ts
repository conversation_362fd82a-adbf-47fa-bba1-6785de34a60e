import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { adminPassword, adminKey } = await request.json()

    // Get admin credentials from environment variables
    const expectedPassword = process.env.ADMIN_SETUP_PASSWORD
    const expectedKey = process.env.ADMIN_SETUP_KEY

    // Verify credentials
    if (!expectedPassword || !expectedKey) {
      return NextResponse.json(
        { error: 'Admin setup not configured' },
        { status: 500 }
      )
    }

    if (adminPassword !== expectedPassword || adminKey !== expectedKey) {
      return NextResponse.json(
        { error: 'Invalid admin credentials' },
        { status: 401 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Admin verification error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
