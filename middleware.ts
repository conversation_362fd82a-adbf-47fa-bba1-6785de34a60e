import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/admin-setup'
  ]

  // API routes that don't require authentication
  const publicApiRoutes = [
    '/api/admin/verify-setup'
  ]

  // Check if the current path is public
  const isPublicRoute = publicRoutes.some(route => pathname === route)
  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route))

  // Allow public routes and API routes
  if (isPublicRoute || isPublicApiRoute) {
    return NextResponse.next()
  }

  // For now, we'll let the client-side handle authentication
  // In a production app, you might want to verify the Firebase token here
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
