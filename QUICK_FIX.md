# 🚨 QUICK FIX for "Missing or insufficient permissions" Error

## Immediate Solution (2 minutes)

### Option 1: Firebase Console (Fastest)

1. **Go to Firebase Console**: https://console.firebase.google.com
2. **Select your project**: `zatconss`
3. **Navigate to**: Firestore Database → Rules
4. **Replace all rules with**:
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /{document=**} {
         allow read, write: if true;
       }
     }
   }
   ```
5. **Click "Publish"**
6. **Test**: Try registering or admin setup again

### Option 2: Command Line (If you have Firebase CLI)

1. **Run**: `deploy-rules.bat` (Windows) or `./deploy-rules.sh` (Mac/Linux)

## Test Your Fix

Visit: http://localhost:3000/test-firestore

This page will test if Firestore is working correctly.

## What This Does

- **Temporarily allows all operations** on Firestore
- **Fixes permission errors** for user registration and admin setup
- **Safe for development** - you can add proper security later

## After Testing

Once everything works, you can implement proper security rules. The project includes templates for production-ready rules.

## Still Having Issues?

1. **Check Firebase project**: Make sure you're using project `zatconss`
2. **Check internet connection**: Firestore needs internet access
3. **Check browser console**: Look for additional error messages
4. **Try the test page**: Visit `/test-firestore` to diagnose issues

## Admin Setup Credentials

- **Password**: `yAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ`
- **Key**: `f62c8e2bf4952eebe5c4c`

Visit: http://localhost:3000/admin-setup
