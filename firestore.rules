rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserData().role == 'admin';
    }
    
    function isOrganizationMember(orgId) {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/organizations/$(orgId)) &&
             get(/databases/$(database)/documents/organizations/$(orgId)).data.members[request.auth.uid] != null;
    }
    
    function isOrganizationAdmin(orgId) {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/organizations/$(orgId)) &&
             get(/databases/$(database)/documents/organizations/$(orgId)).data.members[request.auth.uid].role == 'admin';
    }

    // Users collection - users can read/write their own data, admins can manage all users
    match /users/{userId} {
      allow read, write: if isOwner(userId) || isAdmin();
      allow create: if isAuthenticated() && isOwner(userId);
    }

    // Organizations collection - members can read, admins can write
    match /organizations/{orgId} {
      allow read: if isOrganizationMember(orgId) || isAdmin();
      allow write: if isOrganizationAdmin(orgId) || isAdmin();
      allow create: if isAuthenticated();
    }

    // Projects collection - organization members can read, project admins can write
    match /projects/{projectId} {
      allow read: if isAuthenticated() && 
                     (resource.data.members[request.auth.uid] != null || 
                      isOrganizationMember(resource.data.organizationId) || 
                      isAdmin());
      allow write: if isAuthenticated() && 
                      (resource.data.members[request.auth.uid].role == 'admin' || 
                       isOrganizationAdmin(resource.data.organizationId) || 
                       isAdmin());
      allow create: if isAuthenticated();
    }

    // Boards collection - project members can read/write
    match /boards/{boardId} {
      allow read, write: if isAuthenticated() && 
                            (get(/databases/$(database)/documents/projects/$(resource.data.projectId)).data.members[request.auth.uid] != null || 
                             isAdmin());
      allow create: if isAuthenticated();
    }

    // Tasks collection - project members can read/write
    match /tasks/{taskId} {
      allow read, write: if isAuthenticated() && 
                            (get(/databases/$(database)/documents/projects/$(resource.data.projectId)).data.members[request.auth.uid] != null || 
                             isAdmin());
      allow create: if isAuthenticated();
    }

    // Comments collection - project members can read/write
    match /comments/{commentId} {
      allow read, write: if isAuthenticated() && 
                            (get(/databases/$(database)/documents/tasks/$(resource.data.taskId)).data != null || 
                             isAdmin());
      allow create: if isAuthenticated();
    }

    // Chat sessions - users can access their own sessions, admins can access all
    match /chatSessions/{sessionId} {
      allow read, write: if isAuthenticated() && 
                            (resource.data.userId == request.auth.uid || 
                             isAdmin());
      allow create: if isAuthenticated();
    }

    // Activity logs - read-only for organization members, write for system
    match /activityLogs/{logId} {
      allow read: if isAuthenticated() && 
                     (isOrganizationMember(resource.data.organizationId) || 
                      isAdmin());
      allow write: if isAdmin();
      allow create: if isAuthenticated();
    }

    // Allow all operations for admin users as fallback
    match /{document=**} {
      allow read, write: if isAdmin();
    }
  }
}
