import { 
  collection, 
  getDocs, 
  doc, 
  setDoc, 
  getDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
  query,
  orderBy,
  where,
  limit as firestoreLimit
} from 'firebase/firestore'
import { 
  createUserWithEmailAndPassword,
  updateProfile,
  deleteUser as deleteFirebaseUser,
  User as FirebaseUser
} from 'firebase/auth'
import { auth, db } from '@/lib/firebase'
import { User } from '@/lib/types'

export interface CreateUserData {
  email: string
  displayName: string
  password: string
  role: 'admin' | 'user'
}

export interface UpdateUserData {
  displayName?: string
  role?: 'admin' | 'user'
  preferences?: {
    theme?: 'light' | 'dark'
    notifications?: boolean
  }
}

export class UserManagementService {
  /**
   * Get all users (admin only)
   */
  static async getAllUsers(limit?: number): Promise<User[]> {
    try {
      let usersQuery = query(
        collection(db, 'users'),
        orderBy('createdAt', 'desc')
      )

      if (limit) {
        usersQuery = query(usersQuery, firestoreLimit(limit))
      }

      const snapshot = await getDocs(usersQuery)
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as User))
    } catch (error) {
      console.error('Error getting all users:', error)
      throw error
    }
  }

  /**
   * Get users by role
   */
  static async getUsersByRole(role: 'admin' | 'user'): Promise<User[]> {
    try {
      const usersQuery = query(
        collection(db, 'users'),
        where('role', '==', role),
        orderBy('createdAt', 'desc')
      )

      const snapshot = await getDocs(usersQuery)
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as User))
    } catch (error) {
      console.error('Error getting users by role:', error)
      throw error
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId))
      if (userDoc.exists()) {
        return { id: userDoc.id, ...userDoc.data() } as User
      }
      return null
    } catch (error) {
      console.error('Error getting user by ID:', error)
      throw error
    }
  }

  /**
   * Create a new user (admin only)
   */
  static async createUser(userData: CreateUserData): Promise<User> {
    try {
      // Create Firebase user
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      )

      // Update display name
      await updateProfile(userCredential.user, {
        displayName: userData.displayName
      })

      // Create user document in Firestore
      const newUser: User = {
        id: userCredential.user.uid,
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        preferences: {
          theme: 'light',
          notifications: true
        }
      }

      await setDoc(doc(db, 'users', userCredential.user.uid), newUser)
      return newUser
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  }

  /**
   * Update user data (admin only)
   */
  static async updateUser(userId: string, updateData: UpdateUserData): Promise<void> {
    try {
      const userRef = doc(db, 'users', userId)
      
      const updatedData: any = {
        ...updateData,
        updatedAt: Timestamp.now()
      }

      await updateDoc(userRef, updatedData)
    } catch (error) {
      console.error('Error updating user:', error)
      throw error
    }
  }

  /**
   * Delete user (admin only)
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      // Delete user document from Firestore
      await deleteDoc(doc(db, 'users', userId))
      
      // Note: Deleting Firebase Auth user requires admin SDK on server side
      // For now, we'll just delete the Firestore document
      // The Firebase Auth user will remain but won't have access to the app
    } catch (error) {
      console.error('Error deleting user:', error)
      throw error
    }
  }

  /**
   * Toggle user role between admin and user
   */
  static async toggleUserRole(userId: string): Promise<void> {
    try {
      const user = await this.getUserById(userId)
      if (!user) {
        throw new Error('User not found')
      }

      const newRole = user.role === 'admin' ? 'user' : 'admin'
      await this.updateUser(userId, { role: newRole })
    } catch (error) {
      console.error('Error toggling user role:', error)
      throw error
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStats(): Promise<{
    totalUsers: number
    adminUsers: number
    regularUsers: number
    recentUsers: User[]
  }> {
    try {
      const [allUsers, adminUsers, regularUsers, recentUsers] = await Promise.all([
        this.getAllUsers(),
        this.getUsersByRole('admin'),
        this.getUsersByRole('user'),
        this.getAllUsers(5) // Get 5 most recent users
      ])

      return {
        totalUsers: allUsers.length,
        adminUsers: adminUsers.length,
        regularUsers: regularUsers.length,
        recentUsers
      }
    } catch (error) {
      console.error('Error getting user stats:', error)
      throw error
    }
  }

  /**
   * Search users by email or display name
   */
  static async searchUsers(searchTerm: string): Promise<User[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a simple implementation that gets all users and filters client-side
      // For production, consider using Algolia or similar search service
      
      const allUsers = await this.getAllUsers()
      const searchTermLower = searchTerm.toLowerCase()
      
      return allUsers.filter(user => 
        user.email.toLowerCase().includes(searchTermLower) ||
        user.displayName.toLowerCase().includes(searchTermLower)
      )
    } catch (error) {
      console.error('Error searching users:', error)
      throw error
    }
  }

  /**
   * Validate user permissions (check if user is admin)
   */
  static async validateAdminPermissions(userId: string): Promise<boolean> {
    try {
      const user = await this.getUserById(userId)
      return user?.role === 'admin' || false
    } catch (error) {
      console.error('Error validating admin permissions:', error)
      return false
    }
  }

  /**
   * Bulk update users
   */
  static async bulkUpdateUsers(userIds: string[], updateData: UpdateUserData): Promise<void> {
    try {
      const updatePromises = userIds.map(userId => 
        this.updateUser(userId, updateData)
      )
      
      await Promise.all(updatePromises)
    } catch (error) {
      console.error('Error bulk updating users:', error)
      throw error
    }
  }

  /**
   * Get users with pagination
   */
  static async getUsersPaginated(page: number = 1, pageSize: number = 10): Promise<{
    users: User[]
    totalUsers: number
    totalPages: number
    currentPage: number
  }> {
    try {
      // Get total count first
      const allUsers = await this.getAllUsers()
      const totalUsers = allUsers.length
      const totalPages = Math.ceil(totalUsers / pageSize)
      
      // Calculate pagination
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      
      const users = allUsers.slice(startIndex, endIndex)
      
      return {
        users,
        totalUsers,
        totalPages,
        currentPage: page
      }
    } catch (error) {
      console.error('Error getting paginated users:', error)
      throw error
    }
  }
}
