"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-setup/page",{

/***/ "(app-pages-browser)/./src/app/admin-setup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/admin-setup/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminSetupPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,KeyIcon,LockClosedIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,KeyIcon,LockClosedIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,KeyIcon,LockClosedIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,KeyIcon,LockClosedIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,KeyIcon,LockClosedIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon,KeyIcon,LockClosedIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _lib_services_setupService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/setupService */ \"(app-pages-browser)/./src/lib/services/setupService.ts\");\n/* harmony import */ var _components_common_FirestoreErrorHandler__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/common/FirestoreErrorHandler */ \"(app-pages-browser)/./src/components/common/FirestoreErrorHandler.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction AdminSetupPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAdminPassword, setShowAdminPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Security verification form\n    const [securityData, setSecurityData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        adminPassword: \"\",\n        adminKey: \"\"\n    });\n    // Admin user form\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    const handleSecurityVerification = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            // Verify admin credentials with backend\n            const response = await fetch(\"/api/admin/verify-setup\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(securityData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Invalid admin credentials\");\n            }\n            setStep(2);\n        } catch (error) {\n            setError(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAdminCreation = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            setLoading(false);\n            return;\n        }\n        if (formData.password.length < 6) {\n            setError(\"Password must be at least 6 characters\");\n            setLoading(false);\n            return;\n        }\n        try {\n            // Create Firebase user\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, formData.email, formData.password);\n            // Update display name\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.updateProfile)(userCredential.user, {\n                displayName: formData.displayName\n            });\n            // Create admin user and organization\n            await _lib_services_setupService__WEBPACK_IMPORTED_MODULE_5__.SetupService.createAdminUser(userCredential.user, formData.displayName);\n            router.push(\"/dashboard\");\n        } catch (error) {\n            setError(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleAdminSetup = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            const provider = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.GoogleAuthProvider();\n            const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPopup)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, provider);\n            // Create admin user and organization\n            await _lib_services_setupService__WEBPACK_IMPORTED_MODULE_5__.SetupService.createAdminUser(result.user, result.user.displayName || \"\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            setError(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-xl p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Admin Setup\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: step === 1 ? \"Verify admin credentials to proceed\" : \"Create the first admin account\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_FirestoreErrorHandler__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            error: {\n                                message: error\n                            },\n                            onRetry: ()=>setError(\"\"),\n                            onDismiss: ()=>setError(\"\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this),\n                    step === 1 ? // Security Verification Step\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSecurityVerification,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Admin Password\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: showAdminPassword ? \"text\" : \"password\",\n                                                value: securityData.adminPassword,\n                                                onChange: (e)=>setSecurityData({\n                                                        ...securityData,\n                                                        adminPassword: e.target.value\n                                                    }),\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12\",\n                                                placeholder: \"Enter admin password\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setShowAdminPassword(!showAdminPassword),\n                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                children: showAdminPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 42\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 81\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Admin Key\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: securityData.adminKey,\n                                                onChange: (e)=>setSecurityData({\n                                                        ...securityData,\n                                                        adminKey: e.target.value\n                                                    }),\n                                                className: \"w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                placeholder: \"Enter admin key\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Verifying...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Verify Credentials\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, this) : // Admin Creation Step\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleAdminCreation,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Full Name\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.displayName,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        displayName: e.target.value\n                                                    }),\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                placeholder: \"Enter your full name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Email Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: formData.email,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        email: e.target.value\n                                                    }),\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                placeholder: \"Enter your email\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Password\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        value: formData.password,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                password: e.target.value\n                                                            }),\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12\",\n                                                        placeholder: \"Create a password\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 39\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 78\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Confirm Password\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"password\",\n                                                value: formData.confirmPassword,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        confirmPassword: e.target.value\n                                                    }),\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                placeholder: \"Confirm your password\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Creating Admin...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_KeyIcon_LockClosedIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Create Admin Account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full border-t border-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 bg-white text-gray-500\",\n                                            children: \"Or continue with\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGoogleAdminSetup,\n                                disabled: loading,\n                                className: \"w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 mr-2\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fill: \"#4285F4\",\n                                                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fill: \"#34A853\",\n                                                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fill: \"#FBBC05\",\n                                                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fill: \"#EA4335\",\n                                                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Continue with Google\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\admin-setup\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminSetupPage, \"r7HPNNjLwYnFPpxT50cdY7OCT5I=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = AdminSetupPage;\nvar _c;\n$RefreshReg$(_c, \"AdminSetupPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-setup/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/common/FirestoreErrorHandler.tsx":
/*!*********************************************************!*\
  !*** ./src/components/common/FirestoreErrorHandler.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FirestoreErrorHandler; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction FirestoreErrorHandler(param) {\n    let { error, onRetry, onDismiss } = param;\n    const getErrorMessage = (error)=>{\n        if (!error) return null;\n        const errorCode = error.code || error.message || \"\";\n        if (errorCode.includes(\"permission-denied\") || errorCode.includes(\"Missing or insufficient permissions\")) {\n            return {\n                title: \"Permission Denied\",\n                message: \"Firestore security rules need to be deployed.\",\n                solution: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium mb-2\",\n                            children: \"Quick Fix:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-1 text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        \"Run \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-gray-100 px-1 rounded\",\n                                            children: \"deploy-rules.bat\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Or go to Firebase Console → Firestore → Rules\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Set rules to allow all operations temporarily\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        \"See \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-gray-100 px-1 rounded\",\n                                            children: \"FIRESTORE_SETUP.md\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 23\n                                        }, this),\n                                        \" for details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, this),\n                type: \"error\"\n            };\n        }\n        if (errorCode.includes(\"unavailable\")) {\n            return {\n                title: \"Service Unavailable\",\n                message: \"Firestore is temporarily unavailable. Please try again in a moment.\",\n                solution: null,\n                type: \"warning\"\n            };\n        }\n        if (errorCode.includes(\"failed-precondition\")) {\n            return {\n                title: \"Configuration Error\",\n                message: \"There's an issue with the Firebase configuration.\",\n                solution: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 text-sm text-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Please check your Firebase project settings and ensure Firestore is enabled.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this),\n                type: \"error\"\n            };\n        }\n        if (errorCode.includes(\"network-request-failed\")) {\n            return {\n                title: \"Network Error\",\n                message: \"Unable to connect to Firebase. Please check your internet connection.\",\n                solution: null,\n                type: \"warning\"\n            };\n        }\n        // Generic error\n        return {\n            title: \"Error\",\n            message: error.message || \"An unexpected error occurred.\",\n            solution: null,\n            type: \"error\"\n        };\n    };\n    const errorInfo = getErrorMessage(error);\n    if (!errorInfo) return null;\n    const bgColor = errorInfo.type === \"error\" ? \"bg-red-50 border-red-200\" : \"bg-yellow-50 border-yellow-200\";\n    const textColor = errorInfo.type === \"error\" ? \"text-red-800\" : \"text-yellow-800\";\n    const iconColor = errorInfo.type === \"error\" ? \"text-red-400\" : \"text-yellow-400\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg p-4 \".concat(bgColor),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: errorInfo.type === \"error\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"h-5 w-5 \".concat(iconColor)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-5 w-5 \".concat(iconColor)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-3 flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium \".concat(textColor),\n                            children: errorInfo.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-sm \".concat(textColor),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: errorInfo.message\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                errorInfo.solution\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex space-x-3\",\n                            children: [\n                                onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onRetry,\n                                    className: \"text-sm font-medium \".concat(errorInfo.type === \"error\" ? \"text-red-800 hover:text-red-900\" : \"text-yellow-800 hover:text-yellow-900\", \" underline\"),\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                onDismiss && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onDismiss,\n                                    className: \"text-sm font-medium \".concat(errorInfo.type === \"error\" ? \"text-red-800 hover:text-red-900\" : \"text-yellow-800 hover:text-yellow-900\", \" underline\"),\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\components\\\\common\\\\FirestoreErrorHandler.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_c = FirestoreErrorHandler;\nvar _c;\n$RefreshReg$(_c, \"FirestoreErrorHandler\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/FirestoreErrorHandler.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ExclamationTriangleIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n    }));\n}\n_c = ExclamationTriangleIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ExclamationTriangleIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ExclamationTriangleIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction InformationCircleIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z\"\n    }));\n}\n_c = InformationCircleIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(InformationCircleIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"InformationCircleIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\n"));

/***/ })

});