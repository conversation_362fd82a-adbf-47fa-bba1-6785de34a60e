'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowRightIcon, SparklesIcon, UsersIcon, ChartBarIcon } from '@heroicons/react/24/outline'
import { SetupService } from '@/lib/services/setupService'

export default function HomePage() {
  const [loading, setLoading] = useState(true)
  const [isFirstUser, setIsFirstUser] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const checkFirstUser = async () => {
      try {
        const firstUser = await SetupService.isFirstUser()
        if (firstUser) {
          // Redirect to setup page for first user
          router.push('/setup')
          return
        }
        setIsFirstUser(false)
      } catch (error) {
        console.error('Error checking first user:', error)
      } finally {
        setLoading(false)
      }
    }

    checkFirstUser()
  }, [router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }
    return (
        <div className="min-h-screen bg-gradient-to-br from-primary-50 to-indigo-100">
            {/* Header */}
            <header className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <div className="flex items-center">
                            <SparklesIcon className="h-8 w-8 text-primary-600" />
                            <span className="ml-2 text-xl font-bold text-gray-900">AI Productivity</span>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Link href="/auth/login" className="text-gray-600 hover:text-gray-900">
                                Sign In
                            </Link>
                            <Link href="/auth/register" className="btn-primary">
                                Get Started
                            </Link>
                        </div>
                    </div>
                </div>
            </header>

            {/* Hero Section */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                <div className="text-center">
                    <h1 className="text-4xl font-bold text-gray-900 sm:text-6xl">
                        Productivity Platform with
                        <span className="text-primary-600"> AI Superpowers</span>
                    </h1>
                    <p className="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
                        Manage projects, collaborate with teams, and boost productivity with intelligent AI assistance.
                        Create tasks, organize boards, and let AI help you stay on top of everything.
                    </p>
                    <div className="mt-10 flex justify-center gap-4">
                        <Link href="/auth/register" className="btn-primary flex items-center">
                            Start Free Trial
                            <ArrowRightIcon className="ml-2 h-4 w-4" />
                        </Link>
                        <Link href="#features" className="btn-secondary">
                            Learn More
                        </Link>
                    </div>
                </div>

                {/* Features */}
                <div id="features" className="mt-24">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="card p-6 text-center">
                            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <SparklesIcon className="h-6 w-6 text-primary-600" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Assistant</h3>
                            <p className="text-gray-600">
                                Intelligent AI that helps create tasks, schedule events, and provides smart suggestions for your workflow.
                            </p>
                        </div>

                        <div className="card p-6 text-center">
                            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <UsersIcon className="h-6 w-6 text-primary-600" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Team Collaboration</h3>
                            <p className="text-gray-600">
                                Real-time collaboration with kanban boards, task assignments, and team communication features.
                            </p>
                        </div>

                        <div className="card p-6 text-center">
                            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <ChartBarIcon className="h-6 w-6 text-primary-600" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Project Management</h3>
                            <p className="text-gray-600">
                                Organize projects with kanban boards, to-do lists, notes, and event scheduling all in one place.
                            </p>
                        </div>
                    </div>
                </div>

                {/* Admin Setup Link */}
                <div className="mt-16 text-center">
                    <p className="text-sm text-gray-500">
                        System Administrator?{' '}
                        <Link
                            href="/admin-setup"
                            className="font-medium text-primary-600 hover:text-primary-500"
                        >
                            Set up admin account
                        </Link>
                    </p>
                </div>
            </main>
        </div>
    )
}