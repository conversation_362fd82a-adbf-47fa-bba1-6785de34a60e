'use client'

import { ExclamationTriangleIcon, InformationCircleIcon } from '@heroicons/react/24/outline'

interface FirestoreErrorHandlerProps {
  error: any
  onRetry?: () => void
  onDismiss?: () => void
}

export default function FirestoreErrorHandler({ error, onRetry, onDismiss }: FirestoreErrorHandlerProps) {
  const getErrorMessage = (error: any) => {
    if (!error) return null

    const errorCode = error.code || error.message || ''
    
    if (errorCode.includes('permission-denied') || errorCode.includes('Missing or insufficient permissions')) {
      return {
        title: 'Permission Denied',
        message: 'Firestore security rules need to be deployed.',
        solution: (
          <div className="mt-3 text-sm">
            <p className="font-medium mb-2">Quick Fix:</p>
            <ol className="list-decimal list-inside space-y-1 text-gray-600">
              <li>Run <code className="bg-gray-100 px-1 rounded">deploy-rules.bat</code></li>
              <li>Or go to Firebase Console → Firestore → Rules</li>
              <li>Set rules to allow all operations temporarily</li>
              <li>See <code className="bg-gray-100 px-1 rounded">FIRESTORE_SETUP.md</code> for details</li>
            </ol>
          </div>
        ),
        type: 'error' as const
      }
    }

    if (errorCode.includes('unavailable')) {
      return {
        title: 'Service Unavailable',
        message: 'Firestore is temporarily unavailable. Please try again in a moment.',
        solution: null,
        type: 'warning' as const
      }
    }

    if (errorCode.includes('failed-precondition')) {
      return {
        title: 'Configuration Error',
        message: 'There\'s an issue with the Firebase configuration.',
        solution: (
          <div className="mt-3 text-sm text-gray-600">
            <p>Please check your Firebase project settings and ensure Firestore is enabled.</p>
          </div>
        ),
        type: 'error' as const
      }
    }

    if (errorCode.includes('network-request-failed')) {
      return {
        title: 'Network Error',
        message: 'Unable to connect to Firebase. Please check your internet connection.',
        solution: null,
        type: 'warning' as const
      }
    }

    // Generic error
    return {
      title: 'Error',
      message: error.message || 'An unexpected error occurred.',
      solution: null,
      type: 'error' as const
    }
  }

  const errorInfo = getErrorMessage(error)
  
  if (!errorInfo) return null

  const bgColor = errorInfo.type === 'error' ? 'bg-red-50 border-red-200' : 'bg-yellow-50 border-yellow-200'
  const textColor = errorInfo.type === 'error' ? 'text-red-800' : 'text-yellow-800'
  const iconColor = errorInfo.type === 'error' ? 'text-red-400' : 'text-yellow-400'

  return (
    <div className={`border rounded-lg p-4 ${bgColor}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          {errorInfo.type === 'error' ? (
            <ExclamationTriangleIcon className={`h-5 w-5 ${iconColor}`} />
          ) : (
            <InformationCircleIcon className={`h-5 w-5 ${iconColor}`} />
          )}
        </div>
        <div className="ml-3 flex-1">
          <h3 className={`text-sm font-medium ${textColor}`}>
            {errorInfo.title}
          </h3>
          <div className={`mt-2 text-sm ${textColor}`}>
            <p>{errorInfo.message}</p>
            {errorInfo.solution}
          </div>
          <div className="mt-4 flex space-x-3">
            {onRetry && (
              <button
                onClick={onRetry}
                className={`text-sm font-medium ${
                  errorInfo.type === 'error' 
                    ? 'text-red-800 hover:text-red-900' 
                    : 'text-yellow-800 hover:text-yellow-900'
                } underline`}
              >
                Try Again
              </button>
            )}
            {onDismiss && (
              <button
                onClick={onDismiss}
                className={`text-sm font-medium ${
                  errorInfo.type === 'error' 
                    ? 'text-red-800 hover:text-red-900' 
                    : 'text-yellow-800 hover:text-yellow-900'
                } underline`}
              >
                Dismiss
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
