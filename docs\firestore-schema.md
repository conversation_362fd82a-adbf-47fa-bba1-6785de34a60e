# Firestore Database Schema

## Collections Structure

### 1. Users Collection (`users`)
```typescript
interface User {
  id: string; // Firebase Auth UID
  email: string;
  displayName: string;
  photoURL?: string;
  role: 'admin' | 'user';
  createdAt: Timestamp;
  updatedAt: Timestamp;
  preferences: {
    theme: 'light' | 'dark';
    notifications: boolean;
  };
}
```

### 2. Organizations Collection (`organizations`)
```typescript
interface Organization {
  id: string;
  name: string;
  description?: string;
  ownerId: string; // User ID
  members: {
    [userId: string]: {
      role: 'admin' | 'member';
      joinedAt: Timestamp;
    };
  };
  settings: {
    aiModels: {
      [modelId: string]: {
        enabled: boolean;
        displayName: string;
        provider: 'openrouter';
      };
    };
    mcpEnabled: boolean;
  };
  apiKeys: {
    openrouter: string[]; // Encrypted API keys
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 3. Projects Collection (`projects`)
```typescript
interface Project {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  ownerId: string;
  members: {
    [userId: string]: {
      role: 'admin' | 'member';
      addedAt: Timestamp;
    };
  };
  settings: {
    visibility: 'private' | 'organization';
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 4. Boards Collection (`boards`)
```typescript
interface Board {
  id: string;
  name: string;
  description?: string;
  projectId: string;
  organizationId: string;
  columns: {
    [columnId: string]: {
      id: string;
      title: string;
      position: number;
      taskIds: string[]; // Ordered list of task IDs
    };
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 5. Tasks Collection (`tasks`)
```typescript
interface Task {
  id: string;
  title: string;
  description: string; // Rich HTML content
  boardId: string;
  columnId: string;
  projectId: string;
  organizationId: string;
  assigneeId?: string;
  createdById: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'todo' | 'in-progress' | 'review' | 'done';
  dueDate?: Timestamp;
  labels: string[];
  position: number; // For ordering within column
  subtasks: {
    [subtaskId: string]: {
      id: string;
      title: string;
      completed: boolean;
      createdAt: Timestamp;
    };
  };
  attachments: {
    [attachmentId: string]: {
      id: string;
      name: string;
      url: string;
      type: string;
      size: number;
    };
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 6. Comments Collection (`comments`)
```typescript
interface Comment {
  id: string;
  taskId: string;
  userId: string;
  content: string;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
  edited: boolean;
}
```

### 7. Todo Lists Collection (`todoLists`)
```typescript
interface TodoList {
  id: string;
  title: string;
  projectId: string;
  organizationId: string;
  userId: string; // Owner
  items: {
    [itemId: string]: {
      id: string;
      title: string;
      completed: boolean;
      priority: 'low' | 'medium' | 'high';
      dueDate?: Timestamp;
      tags: string[];
      recurrence?: {
        type: 'daily' | 'weekly' | 'monthly';
        interval: number;
      };
      createdAt: Timestamp;
    };
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 8. Notes Collection (`notes`)
```typescript
interface Note {
  id: string;
  title: string;
  content: string; // Rich HTML content
  projectId: string;
  organizationId: string;
  userId: string; // Owner
  week?: string; // Format: YYYY-WW for weekly notes
  tags: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 9. Events Collection (`events`)
```typescript
interface Event {
  id: string;
  title: string;
  description?: string;
  projectId: string;
  organizationId: string;
  userId: string; // Creator
  startDate: Timestamp;
  endDate: Timestamp;
  allDay: boolean;
  attendees: string[]; // User IDs
  location?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 10. AI Chat Sessions Collection (`chatSessions`)
```typescript
interface ChatSession {
  id: string;
  userId: string;
  projectId?: string;
  organizationId: string;
  title: string;
  messages: {
    [messageId: string]: {
      id: string;
      role: 'user' | 'assistant';
      content: string;
      timestamp: Timestamp;
      model?: string;
      actions?: {
        type: 'create_task' | 'update_task' | 'create_todo' | 'schedule_event';
        data: any;
        approved: boolean;
        executedAt?: Timestamp;
      }[];
    };
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### 11. MCP Sessions Collection (`mcpSessions`)
```typescript
interface MCPSession {
  id: string;
  userId: string;
  organizationId: string;
  serverHost: string;
  serverPort: number;
  username: string;
  // SSH key stored encrypted
  status: 'connected' | 'disconnected' | 'error';
  lastActivity: Timestamp;
  commands: {
    [commandId: string]: {
      id: string;
      command: string;
      output: string;
      approved: boolean;
      executedAt: Timestamp;
    };
  };
  createdAt: Timestamp;
}
```

### 12. Activity Logs Collection (`activityLogs`)
```typescript
interface ActivityLog {
  id: string;
  userId: string;
  organizationId: string;
  projectId?: string;
  action: string;
  entityType: 'task' | 'project' | 'board' | 'comment' | 'ai_chat' | 'mcp_command';
  entityId: string;
  details: any;
  timestamp: Timestamp;
}
```

## Security Rules Considerations

1. **User Access**: Users can only access organizations they're members of
2. **Project Scope**: All data is scoped to organization and project levels
3. **Admin Rights**: Only organization admins can modify settings and API keys
4. **MCP Security**: MCP sessions require explicit approval for each command
5. **API Key Encryption**: All API keys stored encrypted in Firestore
6. **Activity Logging**: All significant actions logged for audit trail

## Indexes Required

1. `tasks` collection: `organizationId`, `projectId`, `boardId`, `assigneeId`
2. `comments` collection: `taskId`, `userId`
3. `chatSessions` collection: `userId`, `organizationId`, `projectId`
4. `activityLogs` collection: `organizationId`, `userId`, `timestamp`