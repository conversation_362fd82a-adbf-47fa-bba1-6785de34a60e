"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/register/page",{

/***/ "(app-pages-browser)/./src/app/auth/register/page.tsx":
/*!****************************************!*\
  !*** ./src/app/auth/register/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RegisterPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _lib_services_setupService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/setupService */ \"(app-pages-browser)/./src/lib/services/setupService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction RegisterPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        displayName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleInputChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const createUserDocument = async (user, displayName)=>{\n        // Check if this is the first user (should be admin)\n        const isFirst = await _lib_services_setupService__WEBPACK_IMPORTED_MODULE_6__.SetupService.isFirstUser();\n        if (isFirst) {\n            // First user becomes admin - redirect to setup\n            router.push(\"/setup\");\n            return;\n        }\n        // Create regular user\n        await _lib_services_setupService__WEBPACK_IMPORTED_MODULE_6__.SetupService.createRegularUser(user, displayName);\n    };\n    const handleEmailRegister = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            setLoading(false);\n            return;\n        }\n        if (formData.password.length < 6) {\n            setError(\"Password must be at least 6 characters\");\n            setLoading(false);\n            return;\n        }\n        try {\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, formData.email, formData.password);\n            // Update the user's display name\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.updateProfile)(userCredential.user, {\n                displayName: formData.displayName\n            });\n            // Create user document in Firestore\n            await createUserDocument(userCredential.user, formData.displayName);\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            let errorMessage = \"Registration failed\";\n            switch(error.code){\n                case \"auth/email-already-in-use\":\n                    errorMessage = \"An account already exists with this email address\";\n                    break;\n                case \"auth/invalid-email\":\n                    errorMessage = \"Invalid email address\";\n                    break;\n                case \"auth/weak-password\":\n                    errorMessage = \"Password is too weak. Please choose a stronger password\";\n                    break;\n                case \"auth/network-request-failed\":\n                    errorMessage = \"Network error. Please check your connection\";\n                    break;\n                case \"permission-denied\":\n                    errorMessage = \"Permission denied. Please check your Firestore security rules\";\n                    break;\n                default:\n                    errorMessage = error.message || \"Registration failed\";\n            }\n            setError(errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleRegister = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            const provider = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.GoogleAuthProvider();\n            const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPopup)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, provider);\n            // Create user document in Firestore\n            await createUserDocument(result.user, result.user.displayName || \"\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            setError(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Create your account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: [\n                                \"Or\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    href: \"/auth/login\",\n                                    className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                    children: \"sign in to existing account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleEmailRegister,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"displayName\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"displayName\",\n                                            name: \"displayName\",\n                                            type: \"text\",\n                                            required: true,\n                                            className: \"input-field mt-1\",\n                                            placeholder: \"Enter your full name\",\n                                            value: formData.displayName,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"input-field mt-1\",\n                                            placeholder: \"Enter your email\",\n                                            value: formData.email,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"new-password\",\n                                                    required: true,\n                                                    className: \"input-field pr-10\",\n                                                    placeholder: \"Create a password\",\n                                                    value: formData.password,\n                                                    onChange: handleInputChange\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"confirmPassword\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: \"password\",\n                                            autoComplete: \"new-password\",\n                                            required: true,\n                                            className: \"input-field mt-1\",\n                                            placeholder: \"Confirm your password\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleInputChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? \"Creating account...\" : \"Create account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-gray-50 text-gray-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleGoogleRegister,\n                                    disabled: loading,\n                                    className: \"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4285F4\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#34A853\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FBBC05\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#EA4335\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center\",\n                            children: \"By creating an account, you agree to our Terms of Service and Privacy Policy.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(RegisterPage, \"VpLXA3RKs03JBOvzUNNeKGn6sCg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/register/page.tsx\n"));

/***/ })

});