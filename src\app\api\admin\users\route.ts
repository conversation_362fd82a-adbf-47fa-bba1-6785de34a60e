import { NextRequest, NextResponse } from 'next/server'
import { UserManagementService } from '@/lib/services/userManagementService'
import { auth } from '@/lib/firebase'
import { getAuth } from 'firebase-admin/auth'
import { initializeApp, getApps, cert } from 'firebase-admin/app'

// Initialize Firebase Admin SDK if not already initialized
if (!getApps().length) {
  // Note: In production, you should use proper service account credentials
  // For now, we'll use the client SDK for user operations
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')
    const role = searchParams.get('role') as 'admin' | 'user' | null
    const search = searchParams.get('search')

    let result

    if (search) {
      const users = await UserManagementService.searchUsers(search)
      result = {
        users,
        totalUsers: users.length,
        totalPages: 1,
        currentPage: 1
      }
    } else if (role) {
      const users = await UserManagementService.getUsersByRole(role)
      result = {
        users,
        totalUsers: users.length,
        totalPages: 1,
        currentPage: 1
      }
    } else {
      result = await UserManagementService.getUsersPaginated(page, pageSize)
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const userData = await request.json()
    
    // Validate required fields
    if (!userData.email || !userData.displayName || !userData.password || !userData.role) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const newUser = await UserManagementService.createUser(userData)
    
    return NextResponse.json(newUser, { status: 201 })
  } catch (error: any) {
    console.error('Error creating user:', error)
    
    // Handle specific Firebase errors
    if (error.code === 'auth/email-already-in-use') {
      return NextResponse.json(
        { error: 'Email address is already in use' },
        { status: 400 }
      )
    }
    
    if (error.code === 'auth/weak-password') {
      return NextResponse.json(
        { error: 'Password is too weak' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}
