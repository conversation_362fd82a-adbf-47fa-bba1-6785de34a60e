'use client'

import { useState } from 'react'
import { collection, addDoc, getDocs } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export default function TestFirestore() {
  const [status, setStatus] = useState('')
  const [loading, setLoading] = useState(false)

  const testFirestore = async () => {
    setLoading(true)
    setStatus('Testing Firestore connection...')

    try {
      // Test write operation
      setStatus('Testing write operation...')
      const docRef = await addDoc(collection(db, 'test'), {
        message: 'Hello Firestore!',
        timestamp: new Date(),
        test: true
      })
      
      setStatus(`✅ Write successful! Document ID: ${docRef.id}`)
      
      // Test read operation
      setStatus('Testing read operation...')
      const querySnapshot = await getDocs(collection(db, 'test'))
      const docs = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
      
      setStatus(`✅ Read successful! Found ${docs.length} documents. Firestore is working correctly!`)
      
    } catch (error: any) {
      console.error('Firestore test error:', error)
      
      if (error.code === 'permission-denied') {
        setStatus(`❌ Permission denied. Please deploy Firestore security rules first.
        
Instructions:
1. Install Firebase CLI: npm install -g firebase-tools
2. Login: firebase login
3. Deploy rules: firebase deploy --only firestore:rules

Or use the Firebase Console to set rules to allow all operations temporarily.`)
      } else {
        setStatus(`❌ Error: ${error.message}`)
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Firestore Connection Test</h1>
          
          <div className="mb-6">
            <button
              onClick={testFirestore}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Testing...' : 'Test Firestore Connection'}
            </button>
          </div>

          {status && (
            <div className="bg-gray-100 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Test Results:</h3>
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">{status}</pre>
            </div>
          )}

          <div className="mt-8 text-sm text-gray-600">
            <h3 className="font-medium mb-2">What this test does:</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>Attempts to write a test document to Firestore</li>
              <li>Attempts to read documents from Firestore</li>
              <li>Reports any permission or connection issues</li>
            </ul>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-medium text-yellow-800 mb-2">If you see permission errors:</h3>
            <p className="text-sm text-yellow-700">
              Run the <code className="bg-yellow-100 px-1 rounded">deploy-rules.bat</code> script 
              or follow the instructions in <code className="bg-yellow-100 px-1 rounded">FIRESTORE_SETUP.md</code>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
